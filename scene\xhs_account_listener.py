# -*- coding: utf-8 -*-

import json
from datetime import datetime, time

from tikhub import Client
from config.xhs_account_config import xhs_dict
from scene.dingtalk_alerter import DingTalkAlerter
from utils.http_utils import HttpUtils
from utils.string_utils import StringUtils


class XhsAccountListener:
    def __init__(self, logger_):
        self.logger_ = logger_
        self.ding_talk_alert = DingTalkAlerter()
        self.client = Client(base_url="https://api.tikhub.io",
                             api_key="6p0MEmst+HkugHAX7XSqoUVvxgIWhozWj6tp4ib4mWFhqGtUdd4v9z+bXw==",
                             max_retries=3,
                             max_connections=50,
                             proxy=None,
                             timeout=60,
                             max_tasks=50)

    async def sync_new_articles(self):
        for author_id, val in xhs_dict.items():
            await self.query_article_list(author_id, val.get('webhook'))

    async def query_article_list(self, user_id: str, webhook: str):
        articles = []
        notes = await self.get_user_notes(user_id)
        if notes is None:
            notes = await self.get_user_notes(user_id)

        if notes is None:
            # 异常
            self.ding_talk_alert.send("tikhub get_user_notes 请求失败")
            return

        for note in notes:
            publish_time = note.get('create_time')
            """
            if self._is_timestamp_yesterday(publish_time):
                articles.append(self._parse(note))
            """
            article = self._build_article(note)

            if self._is_within_last_three_days(publish_time):
                article["needUpdate"] = 1
            else:
                article["needUpdate"] = 0

            articles.append(article)

        if len(articles) > 0:
            self._notify_webhook(webhook, articles)

    async def get_user_notes(self, user_id: str)->list:
        try:
            cursor = None
            data = await self.client.XiaohongshuWeb.get_user_notes(user_id, cursor)
            if data is None:
                data = await self.client.XiaohongshuWeb.get_user_notes(user_id, cursor)

            data = data.get("data").get("data")
            self.logger_.info(StringUtils.obj_2_json_string(data))

            notes = data.get('notes')
            return notes
        except Exception as e:
            self.logger_.error(e)
            return None


    def _build_article(self, note: dict):
        work_id = note.get('id')
        url = "https://www.xiaohongshu.com/discovery/item/" + work_id

        collect_count = note.get('collected_count')
        comment_count = note.get('comments_count')
        share_count = note.get('share_count')
        like_count = note.get('likes')

        user = note.get('user')
        author_name = user.get('nickname')
        author_id = user.get('userid')
        author_avatar = user.get('images')
        author_url = "https://www.xiaohongshu.com/user/profile/" + author_id

        publish_time = note.get('create_time')
        dt_object = datetime.fromtimestamp(publish_time)  # 本地时间
        publish_time_str = dt_object.strftime("%Y-%m-%d %H:%M:%S")

        return {
            "platform": "小红书",
            "keyword": "",
            "url": url,
            "title": note.get('display_title'),
            "content": note.get('desc'),
            "thumbnailLink": "",
            "publishTime": publish_time_str,
            "locationIp": "",
            "authorName": author_name,
            "authorUrl": author_url,
            "likeCount": like_count,
            "commentCount": comment_count,
            "shareCount": share_count,
            "collectCount": collect_count,
            "recordTime": "2025-05-27 18:53:59",
            "needUpdate": 1
        }

    def _notify_webhook(self, url, articles):
        data = {"articles": articles}
        json_str = json.dumps(data)

        HttpUtils.post(url, data=json_str)

    def _is_within_last_three_days(self, timestamp):
        now = datetime.now().timestamp()  # 当前时间戳，单位秒
        three_days_ago = now - 3 * 24 * 60 * 60  # 三天前时间戳

        return three_days_ago <= timestamp <= now

    def _is_timestamp_yesterday(self, timestamp):
        """判断时间戳是否属于昨天（本地时区）"""
        # 获取当前时间（本地时区）
        now = datetime.now()

        # 计算今天0点的时间戳
        today_start = datetime.combine(now.date(), time.min).timestamp()

        # 计算昨天0点的时间戳
        yesterday_start = today_start - 24 * 3600

        # 判断时间戳是否在昨天范围内 [昨天0点, 今天0点)
        return yesterday_start <= timestamp < today_start

