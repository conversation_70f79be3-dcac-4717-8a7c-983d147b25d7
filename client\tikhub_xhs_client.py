# -*- coding: utf-8 -*-
import time

from common.logger import Logger
from utils.http_utils import HttpUtils
import requests


class TikhubXhsClient:
    def __init__(self,
                 base_url='https://api.tikhub.io',
                 max_retries: int = 3,
                 logger_: Logger = None,
                 authorization: str = "6p0MEmst+HkugHAX7XSqoUVvxgIWhozWj6tp4ib4mWFhqGtUdd4v9z+bXw=="):


        self.base_url = base_url
        self.authorization = authorization
        self.headers = {
            "accept": "application/json",
            "Authorization": f"Bearer {authorization}"
        }
        self._max_retries = max_retries
        self.logger_ = logger_


     def get_user_notes(self, user_id: str, lastCursor: str = None) -> dict:
        """
            查询用户笔记 v2
        """
        endpoint = "/api/v1/xiaohongshu/web_v2/fetch_home_notes"

        url = self.base_url + f"{endpoint}?user_id={user_id}&lastCursor={lastCursor}"
        user_notes: dict = self.__process_request(url=url,headers=self.headers, method="GET")
        return user_notes
    
    def __process_request(self, url: str, headers: dict, method: str, params: dict)-> dict:

        for attempt in range(self._max_retries):
            try:
                if method == "GET":
                    response = HttpUtils.get(url=url, headers=headers)
                else:
                    response = HttpUtils.post(url, data=params, headers=headers)
                if not response or \
                        not response.text.strip() or \
                        not response.content:
                    error_message = "第 {0} 次响应内容为空, 状态码: {1}, URL:{2}".format(attempt + 1,
                                                                                         response.status_code,
                                                                                         response.url)
                    self.logger_.info(error_message)
                    continue
                return response.json()
            except Exception as e:
                raise e


    def get_search_notes(self, keyword: str, page: int = 1, sort: str = "general", noteType: str = "_0"):
        endpoint = "/api/v1/xiaohongshu/web_v2/search_notes"
        url: str =self.base_url + f"{endpoint}?keyword={keyword}&page={page}&sort={sort}&noteType={noteType}"

        search_result: dict = self.__process_request(url= url, headers=self.headers, method="GET")
        return search_result

