# -*- encoding:utf-8 -*-

import time
import datetime


class TimeUtils(object):
    @staticmethod
    def time_add_delta(tm, delta):
        return (datetime.datetime.combine(datetime.date.today(), tm) + delta).time()

    @staticmethod
    def get_yst_datetime():
        today = datetime.datetime.now()
        yesterday = today - datetime.timedelta(days=1)
        return yesterday.strftime('%Y-%m-%d')

    @staticmethod
    def get_current_ts():
        n = datetime.datetime.now()
        date_str = n.strftime("%Y-%m-%d %H:%M:%S")
        return date_str

    @staticmethod
    def dt_str_to_ms(time_str):
        dt = datetime.datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
        timestamp_ms = int(time.mktime(dt.timetuple()) * 1000)

        return timestamp_ms

