# -*- coding: utf-8 -*-

import base64
import hashlib
import time

from bean.dto.at_user_account import AtUserAccount
from bean.dto.author_work import AuthorWork
from dao.repository.at_dy_author_work_record_repository import AtDyAuthorWorkRecordRepository
from dao.repository.at_dy_work_comment_repository import AtDyWorkCommentRepository
from dao.repository.at_wxvideo_author_work_record_repository import AtWXVideoAuthorWorkRecordRepository
from dao.repository.at_wxvideo_work_comment_repository import AtWXVideoWorkCommentRepository
from dao.repository.at_xhs_author_work_record_repository import AtXhsAuthorWorkRecordRepository
from dao.repository.at_xhs_work_comment_repository import AtXhsWorkCommentRepository
from dao.repository.user_author_repository import UserAuthorRepository
from scene.fanshu.worker.qb.api_client import <PERSON>pi<PERSON><PERSON>
from scene.fanshu.worker.qb.models import <PERSON><PERSON><PERSON><PERSON><PERSON>g, DouyinGroupArticle, XiaohongshuGroupArticle, ArticleDetail, \
    Platform, WxVideoGroupArticle
from utils.time_utils import TimeUtils


class DailyUpdater:
    def __init__(self, user_id, logger_):
        self.user_id = user_id
        self.logger_ = logger_

        # Initialize configuration
        config = ApiConfig(
            project_id=10312,
            sign="886dbdc01c9127c2d49af566ce3dd73e"
        )

        # Create API client
        self.api_client: ApiClient = ApiClient(config, self.logger_)

        self.user_author_rep = UserAuthorRepository()

        self.dy_author_work_rep = AtDyAuthorWorkRecordRepository()
        self.xhs_author_work_rep = AtXhsAuthorWorkRecordRepository()
        self.wxvideo_author_work_rep = AtWXVideoAuthorWorkRecordRepository()

        self.dy_work_comment_rep = AtDyWorkCommentRepository()
        self.xhs_work_comment_rep = AtXhsWorkCommentRepository()
        self.wxvideo_work_comment_rep = AtWXVideoWorkCommentRepository()

        self.repo_map = {
            "dy": self.dy_author_work_rep,
            "xhs": self.xhs_author_work_rep,
            "wxvideo": self.wxvideo_author_work_rep
        }

    def get_douyin_author_list(self) -> list:
        return self._get_author_list(self.user_id, 'dy')

    def get_xhs_author_list(self) -> list:
        return self._get_author_list(self.user_id, 'xhs')

    def get_wxvideo_author_list(self) -> list:
        return self._get_author_list(self.user_id, 'wxvideo')

    def update_douyin_qb_info(self, publish_day):
        dy_author_list = self.get_douyin_author_list()
        dy_author_map = {}
        for dy_author in dy_author_list:
            dy_author_map[dy_author.author_id] = dy_author

        article_list = self.dy_author_work_rep.query_work_detail_by_time(publish_day)
        for article in article_list:
            work_id = article.get('work_id')
            url = article.get('url')
            if article.get('author_id') in dy_author_map:
                work_entity = self.dy_author_work_rep.query_work_detail(work_id)
                if len(work_entity) > 0:
                    try:
                        article_detail: ArticleDetail = self.api_client.get_article_detail(
                            Platform.DOUYIN, url)

                        # 更新数据
                        self.dy_author_work_rep.update_work_detail(
                            article_detail.news_read_count,
                            article_detail.news_like_count,
                            article_detail.news_comment_count,
                            article_detail.news_reposts_count,
                            article_detail.news_collect_cnt, work_id)
                    except Exception as e:
                        print(e)

                comment_list = self.api_client.get_article_comments(Platform.DOUYIN, url, 1)
                for comment in comment_list:
                    try:
                        uuid = self.hash_comment(comment.comment_user_id, comment.comment_content,
                                                 comment.comment_posttime)
                        self.dy_work_comment_rep.insert(article.get('author_id'),
                                                        work_id,
                                                        url,
                                                        "dy",
                                                        uuid,
                                                        comment)
                    except Exception as e:
                        print(e)

                time.sleep(2)

    def update_wxvideo_qb_info(self, publish_day):
        wxvideo_author_list = self.get_wxvideo_author_list()
        wxvideo_author_map = {}
        for wxvideo_author in wxvideo_author_list:
            wxvideo_author_map[wxvideo_author.author_id] = wxvideo_author

        article_list = self.wxvideo_author_work_rep.query_work_detail_by_time(publish_day)
        for article in article_list:
            work_id = article.get('work_id')
            url = article.get('url')
            if article.get('author_id') in wxvideo_author_map:
                work_entity = self.wxvideo_author_work_rep.query_work_detail(work_id)
                if len(work_entity) > 0:
                    try:
                        article_detail: ArticleDetail = self.api_client.get_article_detail(
                            Platform.WXVIDEO, work_id)

                        # 更新数据
                        self.wxvideo_author_work_rep.update_work_detail(
                            article_detail.news_read_count,
                            article_detail.news_like_count,
                            article_detail.news_comment_count,
                            article_detail.news_reposts_count,
                            article_detail.news_collect_cnt, work_id)
                    except Exception as e:
                        print(e)

                time.sleep(1)
                comment_list = self.api_client.get_article_comments(Platform.WXVIDEO, work_id, 1)
                for comment in comment_list:
                    try:
                        uuid = self.hash_comment(comment.comment_user_id, comment.comment_content,
                                                 comment.comment_posttime)
                        self.wxvideo_work_comment_rep.insert(article.get('author_id'),
                                                             work_id,
                                                             url,
                                                             "wxvideo",
                                                             uuid,
                                                             comment)
                    except Exception as e:
                        print(e)

                time.sleep(2)

    def update_xhs_qb_info(self, publish_day):
        xhs_author_list = self.get_xhs_author_list()
        xhs_author_map = {}
        for xhs_author in xhs_author_list:
            xhs_author_map[xhs_author.author_id] = xhs_author

        article_list = self.xhs_author_work_rep.query_work_detail_by_time(publish_day)
        for article in article_list:
            work_id = article.get('work_id')
            url = article.get('url')
            if article.get('author_id') in xhs_author_map:
                work_entity = self.xhs_author_work_rep.query_work_detail(work_id)
                if len(work_entity) > 0:
                    try:
                        article_detail: ArticleDetail = self.api_client.get_article_detail(
                            Platform.XIAOHONGSHU, url)

                        # 更新数据
                        self.xhs_author_work_rep.update_work_detail(
                            article_detail.news_read_count,
                            article_detail.news_like_count,
                            article_detail.news_comment_count,
                            article_detail.news_reposts_count,
                            article_detail.news_collect_cnt, work_id)
                    except Exception as e:
                        print(e)

                comment_list = self.api_client.get_article_comments(Platform.XIAOHONGSHU, url, 20)
                for comment in comment_list:
                    try:
                        uuid = self.hash_comment(comment.comment_user_id, comment.comment_content,
                                                 comment.comment_posttime)
                        self.xhs_work_comment_rep.insert(article.get('author_id'),
                                                         work_id,
                                                         url,
                                                         "xhs",
                                                         uuid,
                                                         comment)
                    except Exception as e:
                        print(e)

                time.sleep(2)

    def _select_repo(self, platform):
        return self.repo_map.get(platform)

    def _get_author_list(self, user_id, platform) -> list:
        try:
            author_list = []
            rows = self.user_author_rep.query_author_info(user_id, platform)
            for row in rows:
                author_list.append(UserAuthorRepository.build_author_entity(row))

            return author_list
        except Exception as e:
            self.logger_.error("_get_author_list error, %s", e)

    def query_new_dy_article_list(self, day):
        dy_author_list = self.get_douyin_author_list()
        dy_author_map = {}
        for dy_author in dy_author_list:
            dy_author_map[dy_author.author_id] = dy_author

        article_list = self.api_client.get_all_douyin_articles(day)
        for article in article_list:
            if article.douyin_id in dy_author_map:
                work_url = article.news_url
                work_id = work_url.split('/')[-1]
                # 查看文章是否存在
                work_entity = self.dy_author_work_rep.query_work_detail(work_id)
                if len(work_entity) == 0:
                    self.dy_author_work_rep.insert(self._exchange_dy_qb_article(
                        day=day, work_id=work_id, group_article=article,
                        author=dy_author_map.get(article.douyin_id)))
                    print(article)

    def query_new_xhs_article_list(self, day):
        xhs_author_list = self.get_xhs_author_list()
        xhs_author_map = {}
        for xhs_author in xhs_author_list:
            xhs_author_map[xhs_author.author_id] = xhs_author

        article_list = self.api_client.get_all_xhs_articles(day)
        for article in article_list:
            if article.xiaohongshu_id in xhs_author_map:
                try:
                    article_detail: ArticleDetail = self.api_client.get_article_detail(Platform.XIAOHONGSHU,
                                                                                       article.news_url)
                    work_url = article.news_url
                    work_id = work_url.split('/')[-1]
                    # 查看文章是否存在
                    work_entity = self.xhs_author_work_rep.query_work_detail(work_id)
                    if len(work_entity) == 0:
                        self.xhs_author_work_rep.insert(self._exchange_xhs_qb_article(
                            day=day, work_id=work_id, group_article=article,
                            article_detail=article_detail,
                            author=xhs_author_map.get(article.xiaohongshu_id)))
                        print(article)

                    time.sleep(2)
                except Exception as e:
                    print(e)

    def get_wx_article_detail(self, platform: Platform, eid: str) -> ArticleDetail:
        try:
            article_detail = self.api_client.get_article_detail(Platform.WXVIDEO, eid)
            return article_detail
        except Exception as e1:
            time.sleep(3)
            print(eid)

            article_detail = self.api_client.get_article_detail(Platform.WXVIDEO, eid)
            return article_detail

    def query_new_wxvideo_article_list(self, day):
        wxvideo_author_list = self.get_wxvideo_author_list()
        wxvideo_author_map = {}
        for wxvideo_author in wxvideo_author_list:
            wxvideo_author_map[wxvideo_author.author_id] = wxvideo_author

        article_list = self.api_client.get_all_wxvideo_articles(day)
        for article in article_list:
            if article.wxvideo_id in wxvideo_author_map:
                try:
                    article_detail: ArticleDetail = None
                    try:
                        article_detail = self.api_client.get_article_detail(Platform.WXVIDEO, article.eid)
                    except Exception as e1:
                        print(e1)


                    work_id = article.eid
                    # 查看文章是否存在
                    work_entity = self.wxvideo_author_work_rep.query_work_detail(work_id)
                    if len(work_entity) == 0:
                        self.wxvideo_author_work_rep.insert(self._exchange_wxvideo_qb_article(
                            day=day, work_id=work_id, group_article=article,
                            article_detail=article_detail,
                            author=wxvideo_author_map.get(article.wxvideo_id)))
                        print(article)

                    time.sleep(1)
                except Exception as e:
                    print(e)

    def _exchange_dy_qb_article(self, day, work_id, group_article: DouyinGroupArticle, author: AtUserAccount):
        arthor_work = AuthorWork(
            _id=0,
            platform='dy',
            author_id=group_article.douyin_id,
            author_identity=group_article.douyin_id,
            author_avatar=group_article.download_url,
            author_name=group_article.douyin_name,
            author_url=author.home_url,
            work_id=work_id,
            work_uuid=group_article.news_uuid,
            url=group_article.news_url,
            download_url=group_article.download_url,
            long_url='',
            digest='',
            title=group_article.news_title,
            thumbnail_link='',
            content=group_article.news_content,
            img_urls=group_article.news_img,
            video_urls=group_article.download_url,
            music_url=group_article.music_url,
            music_author_name=group_article.music_author_name,
            music_id='',
            music_name=group_article.music_name,
            publish_time=group_article.news_posttime,
            publish_day=day,
            location_ip=group_article.news_content_ip_location,
            read_count=0,
            like_count=group_article.like_nums,
            comment_count=group_article.comment_nums,
            share_count=group_article.share_nums,
            collect_count=group_article.collection_count,
            text_polarity=-2,
            record_time=TimeUtils.get_current_ts()
        )

        return arthor_work

    def _exchange_xhs_qb_article(self, day, work_id,
                                 group_article: XiaohongshuGroupArticle,
                                 article_detail: ArticleDetail,
                                 author: AtUserAccount):
        arthor_work = AuthorWork(
            _id=0,
            platform='dy',
            author_id=group_article.xiaohongshu_identity,
            author_identity=group_article.xiaohongshu_identity,
            author_avatar=article_detail.media_picurl,
            author_name=article_detail.media_name,
            author_url=author.home_url,
            work_id=work_id,
            work_uuid=group_article.news_uuid,
            url=group_article.news_url,
            download_url='',
            long_url='',
            digest=article_detail.news_digest,
            title=article_detail.news_title,
            thumbnail_link='',
            content=article_detail.news_content,
            img_urls=article_detail.news_img_urls,
            video_urls=article_detail.news_video_urls,
            music_url=article_detail.music_url,
            music_author_name=article_detail.music_author_name,
            music_id=article_detail.music_id,
            music_name=article_detail.music_name,
            publish_time=article_detail.news_posttime,
            publish_day=day,
            location_ip=article_detail.news_content_ip_location,
            read_count=article_detail.news_read_count,
            like_count=article_detail.news_like_count,
            comment_count=article_detail.news_comment_count,
            share_count=article_detail.news_reposts_count,
            collect_count=article_detail.news_collect_cnt,
            text_polarity=-2,
            record_time=TimeUtils.get_current_ts()
        )

        return arthor_work

    def _exchange_wxvideo_qb_article(self, day, work_id,
                                     group_article: WxVideoGroupArticle,
                                     article_detail: ArticleDetail,
                                     author: AtUserAccount):
        arthor_work = AuthorWork(
            _id=0,
            platform='dy',
            author_id=group_article.wxvideo_id,
            author_identity=group_article.wxvideo_id,
            author_avatar=article_detail.media_picurl,
            author_name=article_detail.media_name,
            author_url=author.home_url,
            work_id=work_id,
            work_uuid=work_id,
            url=group_article.news_url,
            download_url='',
            long_url='',
            digest=article_detail.news_digest,
            title=article_detail.news_title,
            thumbnail_link='',
            content=article_detail.news_content,
            img_urls=article_detail.news_img_urls,
            video_urls=article_detail.news_video_urls,
            music_url=article_detail.music_url,
            music_author_name=article_detail.music_author_name,
            music_id=article_detail.music_id,
            music_name=article_detail.music_name,
            publish_time=article_detail.news_posttime,
            publish_day=day,
            location_ip=article_detail.news_content_ip_location,
            read_count=article_detail.news_read_count,
            like_count=article_detail.news_like_count,
            comment_count=article_detail.news_comment_count,
            share_count=article_detail.news_reposts_count,
            collect_count=article_detail.news_collect_cnt,
            text_polarity=-2,
            record_time=TimeUtils.get_current_ts()
        )

        return arthor_work

    def update_dy_article_detail(self, publish_day):
        work_list = self.dy_author_work_rep.query_work_detail_by_time(publish_day)
        for work in work_list:
            self.api_client.get_article_detail(Platform.DOUYIN, work.url)

    def hash_comment(self, comment_user, comment_content, comment_time):
        try:
            input_str = f"{comment_user}:{comment_content}:{comment_time}"
            hash_obj = hashlib.sha256(input_str.encode('utf-8'))
            hash_bytes = hash_obj.digest()
            return base64.b64encode(hash_bytes).decode('utf-8')
        except Exception:
            return None

    def update_douyin_qb_comment(self, publish_day):
        dy_author_list = self.get_douyin_author_list()
        dy_author_map = {}
        for dy_author in dy_author_list:
            dy_author_map[dy_author.author_id] = dy_author

        article_list = self.dy_author_work_rep.query_work_detail_by_time(publish_day)
        for article in article_list:
            work_id = article.get('work_id')
            url = article.get('url')
            if article.get('author_id') in dy_author_map:
                work_entity = self.dy_author_work_rep.query_work_detail(work_id)
                comment_list = self.api_client.get_article_comments(Platform.DOUYIN, url, 1)
                for comment in comment_list:
                    try:
                        uuid = self.hash_comment(comment.comment_user_id, comment.comment_content,
                                                 comment.comment_posttime)
                        self.dy_work_comment_rep.insert(article.get('author_id'),
                                                        work_id,
                                                        url,
                                                        "dy",
                                                        uuid,
                                                        comment)
                    except Exception as e:
                        print(e)

                time.sleep(2)

    def update_wxvideo_qb_comment(self, publish_day):
        wxvideo_author_list = self.get_wxvideo_author_list()
        wxvideo_author_map = {}
        for wxvideo_author in wxvideo_author_list:
            wxvideo_author_map[wxvideo_author.author_id] = wxvideo_author

        article_list = self.wxvideo_author_work_rep.query_work_detail_by_time(publish_day)
        for article in article_list:
            work_id = article.get('work_id')
            url = article.get('url')
            if article.get('author_id') in wxvideo_author_map:
                work_entity = self.wxvideo_author_work_rep.query_work_detail(work_id)

                time.sleep(1)
                comment_list = self.api_client.get_article_comments(Platform.WXVIDEO, work_id, 1)
                for comment in comment_list:
                    try:
                        uuid = self.hash_comment(comment.comment_user_id, comment.comment_content,
                                                 comment.comment_posttime)
                        self.wxvideo_work_comment_rep.insert(article.get('author_id'),
                                                             work_id,
                                                             url,
                                                             "wxvideo",
                                                             uuid,
                                                             comment)
                    except Exception as e:
                        print(e)

                time.sleep(2)

    def update_xhs_qb_comment(self, publish_day):
        xhs_author_list = self.get_xhs_author_list()
        xhs_author_map = {}
        for xhs_author in xhs_author_list:
            xhs_author_map[xhs_author.author_id] = xhs_author

        article_list = self.xhs_author_work_rep.query_work_detail_by_time(publish_day)
        for article in article_list:
            work_id = article.get('work_id')
            url = article.get('url')
            if article.get('author_id') in xhs_author_map:
                work_entity = self.xhs_author_work_rep.query_work_detail(work_id)

                comment_list = self.api_client.get_article_comments(Platform.XIAOHONGSHU, url, 20)
                for comment in comment_list:
                    try:
                        uuid = self.hash_comment(comment.comment_user_id, comment.comment_content,
                                                 comment.comment_posttime)
                        self.xhs_work_comment_rep.insert(article.get('author_id'),
                                                         work_id,
                                                         url,
                                                         "xhs",
                                                         uuid,
                                                         comment)
                    except Exception as e:
                        print(e)

                time.sleep(2)