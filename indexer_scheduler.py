# -*- coding: utf-8 -*-

import json
import os
import sys
import asyncio

from apscheduler.schedulers.background import BackgroundScheduler
from datetime import datetime
import time

from common.env import Env
from scene.keyword_handler import Key<PERSON>ordHandler
from scene.xhs_account_listener import XhsAccountListener

cur_path = os.path.dirname(os.path.abspath(__file__))
project_path = os.path.split(cur_path)[0]
sys.path.append(cur_path)
sys.path.append(project_path)


logger_ = Env().get_main_logger()


def do_job(task_id):
    keyword_handler = KeyWordHandler(logger_)
    asyncio.run(keyword_handler.invoke_task(task_id))


def add_job(scheduler):
    # 西安爱乐：每周一周三执行任务
    scheduler.add_job(
        do_job,
        'cron',
        args=["task-00001"],
        day_of_week='mon,wed',  # 关键修改：同时指定周一和周三
        hour=10,  # 上午10点
        minute=13  # 第13分钟
    )

    # 东盛：每日早上07:00执行任务
    scheduler.add_job(
        do_job,
        'cron',
        args=["task-00002"],
        hour=7,
        minute=9
    )

    # 东盛：每日下午13:00执行任务
    scheduler.add_job(
        do_job,
        'cron',
        args=["task-00003"],
        hour=13,
        minute=19
    )


if __name__ == "__main__":
    # 创建调度器
    scheduler = BackgroundScheduler()
    add_job(scheduler)

    # 启动调度器
    scheduler.start()

    print("定时任务已启动 (按 Ctrl+C 退出)...")

    try:
        # 保持主线程运行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        # 优雅关闭调度器
        scheduler.shutdown()
        print("\n定时任务已停止")

