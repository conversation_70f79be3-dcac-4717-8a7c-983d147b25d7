# -*- coding: utf-8 -*-

from datetime import datetime

from tikhub import Client

from client.dingtalk_linker import DingTalk<PERSON>inker
from dao.model.author_work import AuthorWork
from dao.repository.at_xhs_author_work_record_repository import AtXhsAuthorWorkRecordRepository
from scene.data_sync import DataSync
from scene.dingtalk_alerter import DingTalkAlerter
from utils.string_utils import StringUtils
from utils.time_utils import TimeUtils


class XhsKeywordSearcher:
    def __init__(self, logger_):
        self.logger_ = logger_

        self.dingtalk_linker = DingTalkLinker(logger_)
        self.ding_talk_alert = DingTalkAlerter()
        self.xhs_work_repo = AtXhsAuthorWorkRecordRepository()

        self.client = Client(base_url="https://api.tikhub.io",
                             api_key="6p0MEmst+HkugHAX7XSqoUVvxgIWhozWj6tp4ib4mWFhqGtUdd4v9z+bXw==",
                             max_retries=3,
                             max_connections=50,
                             timeout=60,
                             max_tasks=50)

    async def search_key_word(self, keyword, count, data_sync: DataSync):
        article_map = {}
        notify_list = []

        page = 1
        total_count = 0
        while total_count <= count:
            try:
                is_success, has_more, items = await self._query_keyword_page(keyword, page)
                total_count += len(items)

                for item in items:
                    author_work: AuthorWork = self._build_article(item)
                    if author_work is None:
                        continue

                    if article_map.get(author_work.work_id) is not None:
                        continue

                    article_map[author_work.work_id] = author_work
                    notify_list.append(author_work)

                    try:
                        # 同步数据库
                        work_entity = self.xhs_work_repo.query_work_detail(work_id=author_work.work_id)
                        if len(work_entity) > 0:
                            self.xhs_work_repo.update_work_detail(author_work.content, 0,
                                                                  author_work.like_count,
                                                                  author_work.comment_count,
                                                                  author_work.share_count,
                                                                  author_work.collect_count,
                                                                  author_work.work_id)
                        else:
                            self.xhs_work_repo.insert(author_work)
                    except Exception as e:
                        self.logger_.error(f"search_key_word database error: {e}")

                # 同步数据与多维表
                data_sync.invoke(keyword, notify_list)
                notify_list.clear()

                if not has_more or len(items) == 0:
                    break

                page += 1
            except Exception as e:
                self.logger_.error(f"client.bitable.v1.app_table_record.create failed, error: {e}")

    async def _query_keyword_page(self, keyword,
                                  page: int = 1,
                                  sort: str = "general",
                                  noteType: str = "_0"):
        data = await self.client.XiaohongshuWeb.search_notes(keyword, page, sort, noteType)
        if data is None:
            data = await self.client.XiaohongshuWeb.search_notes(keyword, page, sort, noteType)

        self.logger_.info(StringUtils.obj_2_json_string(data))
        if data is None:
            return False, True, None

        code = data.get("code")
        if code != 200:
            return False, True, None

        data = data.get("data").get("data")
        items = data.get('items')

        return True, len(items) > 0, items

    async def search_notes(self, keyword, page, sort, noteType):
        try:
            data = await self.client.XiaohongshuWeb.search_notes(keyword, page, sort, noteType)
            if data is None:
                data = await self.client.XiaohongshuWeb.search_notes(keyword, page, sort, noteType)

            self.logger_.info(StringUtils.obj_2_json_string(data))

            data = data.get("data").get("data")
            notes = data.get('notes')
            return notes
        except Exception as e:
            self.logger_.error(e)
            return None

    def _get_thumbnailLink(self, note):
        try:
            images_list = note.get('images_list')
            if len(images_list) > 0:
                image = images_list[0]
                return image.get('url')
        except Exception as e:
            return ''

    def _build_article(self, item: dict) -> AuthorWork:
        try:
            note = item.get('note')

            user = note.get('user')
            author_name = user.get('nickname')
            author_id = user.get('userid')
            author_avatar = user.get('images')
            author_url = "https://www.xiaohongshu.com/user/profile/" + author_id

            work_id = note.get('id')
            url = "https://www.xiaohongshu.com/discovery/item/" + work_id
            title = note.get('title')
            content = note.get('desc')
            collect_count = note.get('collected_count')
            comment_count = note.get('comments_count')
            share_count = note.get('shared_count')
            like_count = note.get('liked_count')
            publish_time = note.get('timestamp')
            dt_object = datetime.fromtimestamp(publish_time)  # 本地时间
            publish_time_str = dt_object.strftime("%Y-%m-%d %H:%M:%S")
            publish_day = dt_object.strftime("%Y-%m-%d")

            return AuthorWork(_id=0, platform='xhs', author_id=author_id,
                              author_identity=author_id, author_avatar=author_avatar,
                              author_name=author_name, author_url=author_url,
                              work_id=work_id, work_uuid=work_id, url=url,
                              download_url='', long_url=url, digest=content,
                              title=title, thumbnail_link=self._get_thumbnailLink(note),
                              content=content, img_urls='', video_urls='', music_url='', music_author_name='',
                              music_id='', music_name='', publish_time=publish_time_str,
                              publish_day=publish_day, location_ip='', read_count=0,
                              like_count=like_count, comment_count=comment_count,
                              share_count=share_count, collect_count=collect_count,
                              text_polarity=-1,
                              record_time=TimeUtils.get_current_ts()
                              )
        except Exception as e:
            self.logger_.error(f"{StringUtils.obj_2_json_string(item)}, {e}")
            return None
