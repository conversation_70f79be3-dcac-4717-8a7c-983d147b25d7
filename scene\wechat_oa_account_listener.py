# -*- coding: utf-8 -*-

import asyncio
import json
import numpy
from datetime import datetime, time

from tikhub import Client

from common.env import Env
from utils.http_utils import HttpUtils
from utils.string_utils import StringUtils

from config.xhs_account_config import xhs_dict


class XhsAccountListener:
    def __init__(self, logger_):
        self.logger_ = logger_
        self.client = Client(base_url="https://api.tikhub.io",
                             api_key="6p0MEmst+HkugHAX7XSqoUVvxgIWhozWj6tp4ib4mWFhqGtUdd4v9z+bXw==",
                             max_retries=3,
                             max_connections=50,
                             timeout=60,
                             max_tasks=50)

    async def sync_new_articles(self):
        for author_id, val in xhs_dict.items():
            await self.query_article_list(author_id, val.get('webhook'))

    async def query_article_list(self, user_id: str, webhook: str):
        cursor = None
        data = await self.client.get_user_notes(user_id, cursor)

        if data is None:
            data = await self.client.XiaohongshuWeb.get_user_notes(user_id, cursor)

        data = data.get("data").get("data")
        self.logger_.info(StringUtils.obj_2_json_string(data))

        articles = []
        notes = data.get('notes')
        for note in notes:
            publish_time = note.get('create_time')
            if self._is_timestamp_yesterday(publish_time):
                articles.append(self._parse(note))

        if len(articles) > 0:
            self._notify_webhook(webhook, articles)


    def _is_timestamp_yesterday(self, timestamp):
        """判断时间戳是否属于昨天（本地时区）"""
        # 获取当前时间（本地时区）
        now = datetime.now()

        # 计算今天0点的时间戳
        today_start = datetime.combine(now.date(), time.min).timestamp()

        # 计算昨天0点的时间戳
        yesterday_start = today_start - 24 * 3600

        # 判断时间戳是否在昨天范围内 [昨天0点, 今天0点)
        return yesterday_start <= timestamp < today_start

    def _parse(self, note: dict):
        work_id = note.get('id')
        url = "https://www.xiaohongshu.com/discovery/item/" + work_id

        collect_count = note.get('collected_count')
        comment_count = note.get('comments_count')
        share_count = note.get('share_count')
        like_count = note.get('likes')

        user = note.get('user')
        author_name = user.get('nickname')
        author_id = user.get('userid')
        author_avatar = user.get('images')
        author_url = "https://www.xiaohongshu.com/user/profile/" + author_id

        publish_time = note.get('create_time')
        dt_object = datetime.fromtimestamp(publish_time)  # 本地时间
        publish_time_str = dt_object.strftime("%Y-%m-%d %H:%M:%S")

        return {
            "platform": "小红书",
            "keyword": "",
            "url": url,
            "title": note.get('display_title'),
            "content": note.get('desc'),
            "thumbnailLink": "",
            "publishTime": publish_time_str,
            "locationIp": "",
            "authorName": author_name,
            "authorUrl": author_url,
            "likeCount": like_count,
            "commentCount": comment_count,
            "shareCount": share_count,
            "collectCount": collect_count,
            "recordTime": "2025-05-27 18:53:59",
            "needUpdate": 1
        }

    def _notify_webhook(self, url, articles):
        data = {"articles": articles}
        json_str = json.dumps(data)

        HttpUtils.post(url, data=json_str)

