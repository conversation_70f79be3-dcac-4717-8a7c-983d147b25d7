# -*- coding: utf-8 -*-

keyword_dict = {
    "西安爱乐": ["task-00001"],
    "东盛": ["task-00002", "task-00003"]
}


keyword_task_dict = {
    "task-00001": {
        "company": "西安爱乐",
        "keywords": [
            "陕西大剧院（陕剧）", "西安音乐厅（西厅）", "开元大剧院"
        ],
        "platform": "小红书",
        "cron": "每周一和周三上午 10:13",
        "handler": "dingtalk-bitable-linker",
        "bitable": {
            "webhook": "https://connector.dingtalk.com/webhook/flow/bca99474910679c93dcd82cb",
            "dentryUuid": "YQBnd5ExVE1ydrXxHMz125KMVyeZqMmz",
            "idOrName": "作品数据-常规"
        },
        "count": 200,
    },
    "task-00002": {
        "company": "东盛",
        "keywords": [
            "东盛烤肉", "东盛新品", "东盛自助餐"
        ],
        "platform": "小红书",
        "cron": "每日上午7点",
        "handler": "lark-bitable",
        "bitable": {
            "app_id": "cli_a72e919930b2100d",
            "app_secret": "SfM4aazxDOMIwRbkrlk04fIKampAAUHt",
            "app_token": "ZNWqbeUmoav5n9s9P6pcKeqPn8f",
            "article_table_id": "tbl60AQVX0jas4n1"
        },
        "count": 200
    },
    "task-00003": {
        "company": "东盛",
        "keywords": [
            "东盛烤肉", "东盛自助餐"
        ],
        "platform": "小红书",
        "cron": "每日下午13点",
        "handler": "lark-bitable",
        "bitable": {
            "app_id": "cli_a72e919930b2100d",
            "app_secret": "SfM4aazxDOMIwRbkrlk04fIKampAAUHt",
            "app_token": "ZNWqbeUmoav5n9s9P6pcKeqPn8f",
            "article_table_id": "tbl60AQVX0jas4n1"
        },
        "count": 200
    }
}
